try{self["workbox:window:6.5.4"]&&_()}catch(t){}function t(t,s){return new Promise((i=>{const e=new MessageChannel;e.port1.onmessage=t=>{i(t.data)},t.postMessage(s,[e.port2])}))}try{self["workbox:core:6.5.4"]&&_()}catch(t){}class s{constructor(){this.promise=new Promise(((t,s)=>{this.resolve=t,this.reject=s}))}}class i{constructor(){this.Lt=new Map}addEventListener(t,s){this.It(t).add(s)}removeEventListener(t,s){this.It(t).delete(s)}dispatchEvent(t){t.target=this;const s=this.It(t.type);for(const i of s)i(t)}It(t){return this.Lt.has(t)||this.Lt.set(t,new Set),this.Lt.get(t)}}function e(t,s){const{href:i}=location;return new URL(t,i).href===new URL(s,i).href}class h{constructor(t,s){this.type=t,Object.assign(this,s)}}const n={type:"SKIP_WAITING"};class a extends i{constructor(t,i={}){super(),this.Bt={},this.Tt=0,this.Mt=new s,this.At=new s,this.Gt=new s,this.Kt=0,this.Nt=new Set,this.zt=()=>{const t=this.Dt,s=t.installing;this.Tt>0||!e(s.scriptURL,this.Ft.toString())||performance.now()>this.Kt+6e4?(this.Ht=s,t.removeEventListener("updatefound",this.zt)):(this.Jt=s,this.Nt.add(s),this.Mt.resolve(s)),++this.Tt,s.addEventListener("statechange",this.Qt)},this.Qt=t=>{const s=this.Dt,i=t.target,{state:e}=i,n=i===this.Ht,a={sw:i,isExternal:n,originalEvent:t};!n&&this.Vt&&(a.isUpdate=!0),this.dispatchEvent(new h(e,a)),"installed"===e?this.Xt=self.setTimeout((()=>{"installed"===e&&s.waiting===i&&this.dispatchEvent(new h("waiting",a))}),200):"activating"===e&&(clearTimeout(this.Xt),n||this.At.resolve(i))},this.Yt=t=>{const s=this.Jt,i=s!==navigator.serviceWorker.controller;this.dispatchEvent(new h("controlling",{isExternal:i,originalEvent:t,sw:s,isUpdate:this.Vt})),i||this.Gt.resolve(s)},this.Zt=async t=>{const{data:s,ports:i,source:e}=t;await this.getSW(),this.Nt.has(e)&&this.dispatchEvent(new h("message",{data:s,originalEvent:t,ports:i,sw:e}))},this.Ft=t,this.Bt=i,navigator.serviceWorker.addEventListener("message",this.Zt)}async register({immediate:t=!1}={}){t||"complete"===document.readyState||await new Promise((t=>window.addEventListener("load",t))),this.Vt=Boolean(navigator.serviceWorker.controller),this.ts=this.ss(),this.Dt=await this.es(),this.ts&&(this.Jt=this.ts,this.At.resolve(this.ts),this.Gt.resolve(this.ts),this.ts.addEventListener("statechange",this.Qt,{once:!0}));const s=this.Dt.waiting;return s&&e(s.scriptURL,this.Ft.toString())&&(this.Jt=s,Promise.resolve().then((()=>{this.dispatchEvent(new h("waiting",{sw:s,wasWaitingBeforeRegister:!0}))})).then((()=>{}))),this.Jt&&(this.Mt.resolve(this.Jt),this.Nt.add(this.Jt)),this.Dt.addEventListener("updatefound",this.zt),navigator.serviceWorker.addEventListener("controllerchange",this.Yt),this.Dt}async update(){this.Dt&&await this.Dt.update()}get active(){return this.At.promise}get controlling(){return this.Gt.promise}getSW(){return void 0!==this.Jt?Promise.resolve(this.Jt):this.Mt.promise}async messageSW(s){return t(await this.getSW(),s)}messageSkipWaiting(){this.Dt&&this.Dt.waiting&&t(this.Dt.waiting,n)}ss(){const t=navigator.serviceWorker.controller;return t&&e(t.scriptURL,this.Ft.toString())?t:void 0}async es(){try{const t=await navigator.serviceWorker.register(this.Ft,this.Bt);return this.Kt=performance.now(),t}catch(t){throw t}}}export{a as Workbox,h as WorkboxEvent,t as messageSW};
//# sourceMappingURL=workbox-window.prod.mjs.map
