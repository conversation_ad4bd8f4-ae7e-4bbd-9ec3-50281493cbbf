import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Slide } from '../components/SlideBuilder';

export const exportToPDF = async (slides: Slide[], title: string = 'Presentation') => {
  try {
    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });

    for (let i = 0; i < slides.length; i++) {
      const slide = slides[i];

      if (i > 0) {
        pdf.addPage();
      }

      // Create a temporary div to render the slide
      const slideElement = document.createElement('div');
      slideElement.style.width = '800px';
      slideElement.style.height = '450px';
      slideElement.style.backgroundColor = slide.backgroundColor;
      slideElement.style.position = 'relative';
      slideElement.style.fontFamily = 'Arial, sans-serif';

      // Add components to the slide
      slide.components.forEach(component => {
        const componentElement = document.createElement('div');
        componentElement.style.position = 'absolute';
        componentElement.style.left = `${component.x}px`;
        componentElement.style.top = `${component.y}px`;
        componentElement.style.width = `${component.width}px`;
        componentElement.style.height = `${component.height}px`;

        if (component.type === 'text') {
          componentElement.textContent = component.content || '';
          componentElement.style.fontSize = `${component.fontSize || 16}px`;
          componentElement.style.fontFamily = component.fontFamily || 'Arial';
          componentElement.style.color = component.color || '#000000';
          componentElement.style.backgroundColor = component.backgroundColor || 'transparent';
          componentElement.style.padding = '8px';
          componentElement.style.overflow = 'hidden';
        } else if (component.type === 'shape') {
          componentElement.style.backgroundColor = component.backgroundColor || '#3b82f6';
          componentElement.style.borderRadius = component.style?.borderRadius || '0px';
        } else if (component.type === 'image' && component.content) {
          const img = document.createElement('img');
          img.src = component.content;
          img.style.width = '100%';
          img.style.height = '100%';
          img.style.objectFit = 'contain';
          componentElement.appendChild(img);
        }

        slideElement.appendChild(componentElement);
      });

      // Temporarily add to DOM for rendering
      document.body.appendChild(slideElement);

      // Convert to canvas
      const canvas = await html2canvas(slideElement, {
        width: 800,
        height: 450,
        scale: 2
      });

      // Remove from DOM
      document.body.removeChild(slideElement);

      // Add to PDF
      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', 10, 10, 277, 156); // A4 landscape dimensions

      // Add slide number
      pdf.setFontSize(10);
      pdf.text(`${i + 1} / ${slides.length}`, 280, 200);
    }

    // Save the PDF
    pdf.save(`${title}.pdf`);

    return { success: true };
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    return { success: false, error: 'Failed to export PDF' };
  }
};

export const exportToImages = async (slides: Slide[], title: string = 'Presentation') => {
  try {
    const images: string[] = [];

    for (let i = 0; i < slides.length; i++) {
      const slide = slides[i];

      // Create a temporary div to render the slide
      const slideElement = document.createElement('div');
      slideElement.style.width = '800px';
      slideElement.style.height = '450px';
      slideElement.style.backgroundColor = slide.backgroundColor;
      slideElement.style.position = 'relative';
      slideElement.style.fontFamily = 'Arial, sans-serif';

      // Add components to the slide
      slide.components.forEach(component => {
        const componentElement = document.createElement('div');
        componentElement.style.position = 'absolute';
        componentElement.style.left = `${component.x}px`;
        componentElement.style.top = `${component.y}px`;
        componentElement.style.width = `${component.width}px`;
        componentElement.style.height = `${component.height}px`;

        if (component.type === 'text') {
          componentElement.textContent = component.content || '';
          componentElement.style.fontSize = `${component.fontSize || 16}px`;
          componentElement.style.fontFamily = component.fontFamily || 'Arial';
          componentElement.style.color = component.color || '#000000';
          componentElement.style.backgroundColor = component.backgroundColor || 'transparent';
          componentElement.style.padding = '8px';
          componentElement.style.overflow = 'hidden';
        } else if (component.type === 'shape') {
          componentElement.style.backgroundColor = component.backgroundColor || '#3b82f6';
          componentElement.style.borderRadius = component.style?.borderRadius || '0px';
        } else if (component.type === 'image' && component.content) {
          const img = document.createElement('img');
          img.src = component.content;
          img.style.width = '100%';
          img.style.height = '100%';
          img.style.objectFit = 'contain';
          componentElement.appendChild(img);
        }

        slideElement.appendChild(componentElement);
      });

      // Temporarily add to DOM for rendering
      document.body.appendChild(slideElement);

      // Convert to canvas
      const canvas = await html2canvas(slideElement, {
        width: 800,
        height: 450,
        scale: 2
      });

      // Remove from DOM
      document.body.removeChild(slideElement);

      // Get image data
      const imgData = canvas.toDataURL('image/png');
      images.push(imgData);

      // Download individual image
      const link = document.createElement('a');
      link.download = `${title}_slide_${i + 1}.png`;
      link.href = imgData;
      link.click();
    }

    return { success: true, images };
  } catch (error) {
    console.error('Error exporting to images:', error);
    return { success: false, error: 'Failed to export images' };
  }
};