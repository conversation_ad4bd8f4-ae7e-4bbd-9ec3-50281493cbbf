import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import SlideBuilder, { Slide } from '../components/SlideBuilder.tsx';
import { useAuth } from '../hooks/useAuth.tsx';
import { exportToPDF } from '../utils/exportUtils.ts';

const Editor: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [presentation, setPresentation] = useState<any>(null);
  const [slides, setSlides] = useState<Slide[]>([]);
  const [title, setTitle] = useState('Untitled Presentation');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [error, setError] = useState('');

  // Initialize with a default slide if creating new presentation
  useEffect(() => {
    if (!id) {
      // New presentation
      setSlides([{
        id: `slide-${Date.now()}`,
        type: 'title',
        components: [],
        backgroundColor: '#ffffff'
      }]);
    } else {
      // Load existing presentation
      loadPresentation();
    }
  }, [id]);

  const loadPresentation = async () => {
    if (!id) return;

    setLoading(true);
    try {
      const response = await axios.get(`/presentations/${id}`);
      const data = response.data.data;

      setPresentation(data);
      setTitle(data.title);
      setSlides(data.slides || []);
    } catch (err: any) {
      setError('Failed to load presentation');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const savePresentation = async () => {
    setSaving(true);
    setError('');

    try {
      const presentationData = {
        title,
        slides,
        description: `Presentation with ${slides.length} slides`
      };

      let response;
      if (id) {
        // Update existing presentation
        response = await axios.put(`/presentations/${id}`, presentationData);
      } else {
        // Create new presentation
        response = await axios.post('/presentations', presentationData);
        // Navigate to the new presentation's edit URL
        navigate(`/editor/${response.data.data._id}`, { replace: true });
      }

      setPresentation(response.data.data || response.data);
      setLastSaved(new Date());
    } catch (err: any) {
      setError('Failed to save presentation');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  const handleExportToPDF = async () => {
    try {
      await exportToPDF(slides, title);
    } catch (err: any) {
      setError('Failed to export presentation');
      console.error(err);
    }
  };

  const handleSlidesChange = (newSlides: Slide[]) => {
    setSlides(newSlides);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Top Bar */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/dashboard')}
              className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
            >
              ← Back to Dashboard
            </button>

            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="text-lg font-medium bg-transparent border-none outline-none text-gray-900 dark:text-white"
              placeholder="Presentation Title"
            />
          </div>

          <div className="flex items-center space-x-4">
            {lastSaved && (
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Saved {lastSaved.toLocaleTimeString()}
              </span>
            )}

            {error && (
              <span className="text-sm text-red-600">
                {error}
              </span>
            )}

            <button
              onClick={handleExportToPDF}
              className="btn-secondary text-sm"
            >
              Export PDF
            </button>

            <button
              onClick={savePresentation}
              disabled={saving}
              className="btn-primary text-sm disabled:opacity-50"
            >
              {saving ? 'Saving...' : 'Save'}
            </button>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 overflow-hidden">
        <SlideBuilder
          slides={slides}
          onSlidesChange={handleSlidesChange}
        />
      </div>
    </div>
  );
};

export default Editor;