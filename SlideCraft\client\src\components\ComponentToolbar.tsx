import React from 'react';
import { SlideComponent } from './SlideBuilder';

interface ComponentToolbarProps {
  onAddComponent: (type: SlideComponent['type']) => void;
}

const ComponentToolbar: React.FC<ComponentToolbarProps> = ({ onAddComponent }) => {
  const tools = [
    { type: 'text' as const, icon: '📝', label: 'Text' },
    { type: 'image' as const, icon: '🖼️', label: 'Image' },
    { type: 'shape' as const, icon: '🔷', label: 'Shape' },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center space-x-4">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Add Components:
        </h3>

        {tools.map(({ type, icon, label }) => (
          <button
            key={type}
            onClick={() => onAddComponent(type)}
            className="flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200"
          >
            <span className="text-lg">{icon}</span>
            <span className="text-gray-700 dark:text-gray-300">{label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ComponentToolbar;