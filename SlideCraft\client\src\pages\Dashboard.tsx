import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../hooks/useAuth.tsx';

interface Presentation {
  _id: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  slides: any[];
}

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [presentations, setPresentations] = useState<Presentation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchPresentations();
  }, []);

  const fetchPresentations = async () => {
    try {
      const response = await axios.get('/presentations');
      setPresentations(response.data.data);
    } catch (err: any) {
      setError('Failed to load presentations');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const deletePresentation = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this presentation?')) {
      return;
    }

    try {
      await axios.delete(`/presentations/${id}`);
      setPresentations(presentations.filter(p => p._id !== id));
    } catch (err: any) {
      setError('Failed to delete presentation');
      console.error(err);
    }
  };

  const duplicatePresentation = async (id: string) => {
    try {
      const response = await axios.post(`/presentations/${id}/duplicate`);
      setPresentations([response.data.data, ...presentations]);
    } catch (err: any) {
      setError('Failed to duplicate presentation');
      console.error(err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Welcome back, {user?.name}!
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Manage your presentations and create new ones
        </p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {/* Quick Actions */}
      <div className="grid md:grid-cols-3 gap-6 mb-8">
        <Link
          to="/editor"
          className="card hover:shadow-lg transition-shadow duration-200 text-center"
        >
          <div className="text-4xl mb-4">➕</div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            New Presentation
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Start creating a new presentation from scratch
          </p>
        </Link>

        <div className="card text-center">
          <div className="text-4xl mb-4">📊</div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            {presentations.length} Presentations
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            Total presentations created
          </p>
        </div>

        <div className="card text-center">
          <div className="text-4xl mb-4">⏰</div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Recent Activity
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            {presentations.length > 0
              ? `Last edited: ${new Date(presentations[0].updatedAt).toLocaleDateString()}`
              : 'No recent activity'
            }
          </p>
        </div>
      </div>

      {/* Presentations List */}
      <div className="card">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Your Presentations
          </h2>
          <Link
            to="/editor"
            className="btn-primary"
          >
            Create New
          </Link>
        </div>

        {presentations.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📄</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No presentations yet
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Get started by creating your first presentation
            </p>
            <Link
              to="/editor"
              className="btn-primary"
            >
              Create Your First Presentation
            </Link>
          </div>
        ) : (
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {presentations.map((presentation) => (
              <div
                key={presentation._id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
              >
                <div className="flex justify-between items-start mb-3">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white truncate">
                    {presentation.title}
                  </h3>
                  <div className="flex space-x-1">
                    <button
                      onClick={() => duplicatePresentation(presentation._id)}
                      className="text-gray-400 hover:text-blue-600 p-1"
                      title="Duplicate"
                    >
                      📋
                    </button>
                    <button
                      onClick={() => deletePresentation(presentation._id)}
                      className="text-gray-400 hover:text-red-600 p-1"
                      title="Delete"
                    >
                      🗑️
                    </button>
                  </div>
                </div>

                <p className="text-gray-600 dark:text-gray-400 text-sm mb-3 line-clamp-2">
                  {presentation.description || 'No description'}
                </p>

                <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400 mb-4">
                  <span>
                    Created: {new Date(presentation.createdAt).toLocaleDateString()}
                  </span>
                  <span>
                    {presentation.slides?.length || 0} slides
                  </span>
                </div>

                <Link
                  to={`/editor/${presentation._id}`}
                  className="block w-full text-center btn-primary text-sm py-2"
                >
                  Edit Presentation
                </Link>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;