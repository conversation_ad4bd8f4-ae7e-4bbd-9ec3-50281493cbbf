import React, { useRef, useState } from 'react';
import { useDrag } from 'react-dnd';
import { SlideComponent } from './SlideBuilder';

interface DraggableComponentProps {
  component: SlideComponent;
  isSelected: boolean;
  onSelect: () => void;
  onUpdate: (updates: Partial<SlideComponent>) => void;
  onDoubleClick: () => void;
}

const DraggableComponent: React.FC<DraggableComponentProps> = ({
  component,
  isSelected,
  onSelect,
  onUpdate,
  onDoubleClick
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isResizing, setIsResizing] = useState(false);

  const [{ isDragging }, drag] = useDrag({
    type: 'component',
    item: { id: component.id },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drag(ref);

  // Handle resize
  const handleMouseDown = (e: React.MouseEvent, direction: string) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);

    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = component.width;
    const startHeight = component.height;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      let newWidth = startWidth;
      let newHeight = startHeight;

      if (direction.includes('right')) {
        newWidth = Math.max(20, startWidth + deltaX);
      }
      if (direction.includes('bottom')) {
        newHeight = Math.max(20, startHeight + deltaY);
      }

      onUpdate({ width: newWidth, height: newHeight });
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const renderContent = () => {
    switch (component.type) {
      case 'text':
        return (
          <div
            className="w-full h-full p-2 overflow-hidden"
            style={{
              fontSize: component.fontSize,
              fontFamily: component.fontFamily,
              color: component.color,
              backgroundColor: component.backgroundColor,
            }}
          >
            {component.content || 'Text'}
          </div>
        );
      case 'image':
        return (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            {component.content ? (
              <img
                src={component.content}
                alt="Slide content"
                className="max-w-full max-h-full object-contain"
              />
            ) : (
              <span className="text-gray-500">Image</span>
            )}
          </div>
        );
      case 'shape':
        return (
          <div
            className="w-full h-full"
            style={{
              backgroundColor: component.backgroundColor || '#3b82f6',
              borderRadius: component.style?.borderRadius || 0,
            }}
          />
        );
      default:
        return <div>Unknown component</div>;
    }
  };

  return (
    <div
      ref={ref}
      className={`slide-component ${isSelected ? 'selected' : ''} ${isDragging ? 'dragging' : ''}`}
      style={{
        left: component.x,
        top: component.y,
        width: component.width,
        height: component.height,
        cursor: isDragging ? 'grabbing' : 'grab',
      }}
      onClick={(e) => {
        e.stopPropagation();
        onSelect();
      }}
      onDoubleClick={(e) => {
        e.stopPropagation();
        onDoubleClick();
      }}
    >
      {renderContent()}

      {/* Resize handles */}
      {isSelected && !isDragging && (
        <>
          {/* Bottom-right resize handle */}
          <div
            className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 cursor-se-resize"
            onMouseDown={(e) => handleMouseDown(e, 'bottom-right')}
          />
          {/* Right resize handle */}
          <div
            className="absolute top-1/2 -right-1 w-2 h-6 bg-blue-500 cursor-e-resize transform -translate-y-1/2"
            onMouseDown={(e) => handleMouseDown(e, 'right')}
          />
          {/* Bottom resize handle */}
          <div
            className="absolute -bottom-1 left-1/2 w-6 h-2 bg-blue-500 cursor-s-resize transform -translate-x-1/2"
            onMouseDown={(e) => handleMouseDown(e, 'bottom')}
          />
        </>
      )}
    </div>
  );
};

export default DraggableComponent;