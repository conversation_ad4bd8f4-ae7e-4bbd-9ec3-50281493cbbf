import { api, presentationService, authService, userService, aiService } from '../src/utils/api';

describe('API Utility', () => {
  it('should have a base URL', () => {
    expect(api.defaults.baseURL).toBeDefined();
  });

  it('should provide presentationService methods', () => {
    expect(typeof presentationService.getAll).toBe('function');
    expect(typeof presentationService.getById).toBe('function');
    expect(typeof presentationService.create).toBe('function');
    expect(typeof presentationService.update).toBe('function');
    expect(typeof presentationService.delete).toBe('function');
    expect(typeof presentationService.duplicate).toBe('function');
  });

  it('should provide authService methods', () => {
    expect(typeof authService.login).toBe('function');
    expect(typeof authService.register).toBe('function');
    expect(typeof authService.getMe).toBe('function');
    expect(typeof authService.updateProfile).toBe('function');
  });

  it('should provide userService methods', () => {
    expect(typeof userService.getProfile).toBe('function');
    expect(typeof userService.updateProfile).toBe('function');
    expect(typeof userService.deleteAccount).toBe('function');
  });

  it('should provide aiService methods', () => {
    expect(typeof aiService.suggestOutline).toBe('function');
    expect(typeof aiService.generateContent).toBe('function');
  });
});
