import axios from 'axios';

// API configuration
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
const AI_API_URL = process.env.REACT_APP_AI_API_URL || 'http://localhost:5001';

// Create axios instances
export const api = axios.create({
  baseURL: API_BASE_URL,
});

export const aiApi = axios.create({
  baseURL: AI_API_URL,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors and show user-friendly messages
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    } else if (error.response?.data?.message) {
      // Optionally, show a toast or alert here for user-friendly error
      // alert(error.response.data.message);
      console.error('API Error:', error.response.data.message);
    }
    return Promise.reject(error);
  }
);

// AI API functions
export const aiService = {
  suggestOutline: async (topic: string) => {
    const response = await aiApi.post('/suggest-outline', { topic });
    return response.data;
  },

  generateContent: async (title: string, type: string) => {
    const response = await aiApi.post('/generate-content', { title, type });
    return response.data;
  }
};

// Presentation API functions
export const presentationService = {
  getAll: () => api.get('/presentations'),
  getById: (id: string) => api.get(`/presentations/${id}`),
  create: (data: any) => api.post('/presentations', data),
  update: (id: string, data: any) => api.put(`/presentations/${id}`, data),
  delete: (id: string) => api.delete(`/presentations/${id}`),
  duplicate: (id: string) => api.post(`/presentations/${id}/duplicate`)
};

// Auth API functions
export const authService = {
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),
  register: (name: string, email: string, password: string) =>
    api.post('/auth/register', { name, email, password }),
  getMe: () => api.get('/auth/me'),
  updateProfile: (data: any) => api.put('/auth/updateprofile', data)
};

// User API functions
export const userService = {
  getProfile: () => api.get('/users/profile'),
  updateProfile: (data: any) => api.put('/users/profile', data),
  deleteAccount: () => api.delete('/users/profile')
};