import React from 'react';
import { Slide } from './SlideBuilder';

interface SlideListProps {
  slides: Slide[];
  selectedSlideIndex: number;
  onSlideSelect: (index: number) => void;
  onSlideAdd: (type: Slide['type']) => void;
  onSlideRemove: (index: number) => void;
}

const SlideList: React.FC<SlideListProps> = ({
  slides,
  selectedSlideIndex,
  onSlideSelect,
  onSlideAdd,
  onSlideRemove
}) => {
  const slideTypes: { type: Slide['type']; label: string }[] = [
    { type: 'title', label: 'Title Slide' },
    { type: 'image-text', label: 'Image & Text' },
    { type: 'bullet-list', label: 'Bullet List' },
    { type: 'chart', label: 'Chart' },
  ];

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white mb-3">
          Slides ({slides.length})
        </h2>

        {/* Add slide dropdown */}
        <div className="relative">
          <select
            className="w-full p-2 text-sm bg-green-500 text-white rounded hover:bg-green-600 cursor-pointer"
            onChange={(e) => {
              if (e.target.value) {
                onSlideAdd(e.target.value as Slide['type']);
                e.target.value = '';
              }
            }}
            value=""
          >
            <option value="">+ Add Slide</option>
            {slideTypes.map(({ type, label }) => (
              <option key={type} value={type} className="text-black">
                {label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Slide thumbnails */}
      <div className="flex-1 overflow-y-auto p-2">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`mb-3 p-3 rounded-lg cursor-pointer transition-all duration-200 ${selectedSlideIndex === index
                ? 'bg-blue-500 text-white shadow-md'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            onClick={() => onSlideSelect(index)}
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">
                Slide {index + 1}
              </span>
              {slides.length > 1 && (
                <button
                  className="text-xs px-2 py-1 rounded bg-red-500 text-white hover:bg-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    onSlideRemove(index);
                  }}
                >
                  ×
                </button>
              )}
            </div>

            {/* Slide preview */}
            <div
              className="w-full h-16 rounded border-2 border-gray-300 dark:border-gray-600 relative overflow-hidden"
              style={{ backgroundColor: slide.backgroundColor }}
            >
              {/* Mini preview of components */}
              {slide.components.slice(0, 3).map((component, idx) => (
                <div
                  key={component.id}
                  className="absolute bg-gray-400 dark:bg-gray-500 rounded"
                  style={{
                    left: `${(component.x / 800) * 100}%`,
                    top: `${(component.y / 450) * 100}%`,
                    width: `${Math.min((component.width / 800) * 100, 20)}%`,
                    height: `${Math.min((component.height / 450) * 100, 20)}%`,
                  }}
                />
              ))}

              {slide.components.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center text-xs text-gray-500">
                  Empty
                </div>
              )}
            </div>

            <div className="mt-2 text-xs opacity-75">
              {slide.type} • {slide.components.length} components
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SlideList;