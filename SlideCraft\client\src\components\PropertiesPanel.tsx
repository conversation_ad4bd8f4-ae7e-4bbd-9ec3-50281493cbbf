import React from 'react';
import { Slide, SlideComponent } from './SlideBuilder';

interface PropertiesPanelProps {
  slide: Slide | null;
  selectedComponent: SlideComponent | null;
  onComponentUpdate: (id: string, updates: Partial<SlideComponent>) => void;
  onSlideUpdate: (backgroundColor: string) => void;
}

const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  slide,
  selectedComponent,
  onComponentUpdate,
  onSlideUpdate
}) => {
  if (!slide) {
    return (
      <div className="h-full p-4">
        <div className="text-gray-500 text-center">
          No slide selected
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-800 dark:text-white">
          Properties
        </h2>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {/* Slide Properties */}
        <div>
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Slide Settings
          </h3>

          <div className="space-y-3">
            <div>
              <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                Background Color
              </label>
              <input
                type="color"
                value={slide.backgroundColor}
                onChange={(e) => onSlideUpdate(e.target.value)}
                className="w-full h-8 rounded border border-gray-300 dark:border-gray-600"
              />
            </div>
          </div>
        </div>

        {/* Component Properties */}
        {selectedComponent ? (
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Component Settings
            </h3>

            <div className="space-y-3">
              {/* Position */}
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                    X Position
                  </label>
                  <input
                    type="number"
                    value={selectedComponent.x}
                    onChange={(e) => onComponentUpdate(selectedComponent.id, { x: parseInt(e.target.value) || 0 })}
                    className="input-field text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                    Y Position
                  </label>
                  <input
                    type="number"
                    value={selectedComponent.y}
                    onChange={(e) => onComponentUpdate(selectedComponent.id, { y: parseInt(e.target.value) || 0 })}
                    className="input-field text-sm"
                  />
                </div>
              </div>

              {/* Size */}
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                    Width
                  </label>
                  <input
                    type="number"
                    value={selectedComponent.width}
                    onChange={(e) => onComponentUpdate(selectedComponent.id, { width: parseInt(e.target.value) || 1 })}
                    className="input-field text-sm"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                    Height
                  </label>
                  <input
                    type="number"
                    value={selectedComponent.height}
                    onChange={(e) => onComponentUpdate(selectedComponent.id, { height: parseInt(e.target.value) || 1 })}
                    className="input-field text-sm"
                  />
                </div>
              </div>

              {/* Text-specific properties */}
              {selectedComponent.type === 'text' && (
                <>
                  <div>
                    <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                      Text Content
                    </label>
                    <textarea
                      value={selectedComponent.content || ''}
                      onChange={(e) => onComponentUpdate(selectedComponent.id, { content: e.target.value })}
                      className="input-field text-sm h-20 resize-none"
                      placeholder="Enter text..."
                    />
                  </div>

                  <div>
                    <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                      Font Size
                    </label>
                    <input
                      type="number"
                      value={selectedComponent.fontSize || 16}
                      onChange={(e) => onComponentUpdate(selectedComponent.id, { fontSize: parseInt(e.target.value) || 16 })}
                      className="input-field text-sm"
                      min="8"
                      max="72"
                    />
                  </div>

                  <div>
                    <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                      Font Family
                    </label>
                    <select
                      value={selectedComponent.fontFamily || 'Arial'}
                      onChange={(e) => onComponentUpdate(selectedComponent.id, { fontFamily: e.target.value })}
                      className="input-field text-sm"
                    >
                      <option value="Arial">Arial</option>
                      <option value="Helvetica">Helvetica</option>
                      <option value="Times New Roman">Times New Roman</option>
                      <option value="Georgia">Georgia</option>
                      <option value="Verdana">Verdana</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                      Text Color
                    </label>
                    <input
                      type="color"
                      value={selectedComponent.color || '#000000'}
                      onChange={(e) => onComponentUpdate(selectedComponent.id, { color: e.target.value })}
                      className="w-full h-8 rounded border border-gray-300 dark:border-gray-600"
                    />
                  </div>
                </>
              )}

              {/* Image-specific properties */}
              {selectedComponent.type === 'image' && (
                <div>
                  <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                    Image URL
                  </label>
                  <input
                    type="url"
                    value={selectedComponent.content || ''}
                    onChange={(e) => onComponentUpdate(selectedComponent.id, { content: e.target.value })}
                    className="input-field text-sm"
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
              )}

              {/* Shape-specific properties */}
              {selectedComponent.type === 'shape' && (
                <div>
                  <label className="block text-xs text-gray-600 dark:text-gray-400 mb-1">
                    Background Color
                  </label>
                  <input
                    type="color"
                    value={selectedComponent.backgroundColor || '#3b82f6'}
                    onChange={(e) => onComponentUpdate(selectedComponent.id, { backgroundColor: e.target.value })}
                    className="w-full h-8 rounded border border-gray-300 dark:border-gray-600"
                  />
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="text-gray-500 text-sm text-center">
            Select a component to edit its properties
          </div>
        )}
      </div>
    </div>
  );
};

export default PropertiesPanel;