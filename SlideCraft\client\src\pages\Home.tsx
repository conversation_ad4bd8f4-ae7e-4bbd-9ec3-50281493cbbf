import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth.tsx';

const Home: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Hero Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
            Create Amazing
            <span className="text-blue-600 dark:text-blue-400"> Presentations</span>
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto">
            SlideCraft is the modern way to create, edit, and share presentations.
            Build stunning slides with our intuitive drag-and-drop editor.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {isAuthenticated ? (
              <>
                <Link
                  to="/dashboard"
                  className="btn-primary text-lg px-8 py-3"
                >
                  Go to Dashboard
                </Link>
                <Link
                  to="/editor"
                  className="btn-secondary text-lg px-8 py-3"
                >
                  Create New Presentation
                </Link>
              </>
            ) : (
              <>
                <Link
                  to="/register"
                  className="btn-primary text-lg px-8 py-3"
                >
                  Get Started Free
                </Link>
                <Link
                  to="/login"
                  className="btn-secondary text-lg px-8 py-3"
                >
                  Sign In
                </Link>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Why Choose SlideCraft?
          </h2>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Everything you need to create professional presentations
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <div className="card text-center">
            <div className="text-4xl mb-4">🎨</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Drag & Drop Editor
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Intuitive interface with drag-and-drop functionality.
              Add text, images, and shapes with ease.
            </p>
          </div>

          <div className="card text-center">
            <div className="text-4xl mb-4">🤖</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              AI-Powered Suggestions
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Get intelligent content suggestions and slide layouts
              powered by artificial intelligence.
            </p>
          </div>

          <div className="card text-center">
            <div className="text-4xl mb-4">📤</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Export & Share
            </h3>
            <p className="text-gray-600 dark:text-gray-300">
              Export your presentations as PDF or share them online
              with a simple link.
            </p>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-blue-600 dark:bg-blue-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of users creating amazing presentations with SlideCraft
            </p>
            {!isAuthenticated && (
              <Link
                to="/register"
                className="bg-white text-blue-600 hover:bg-gray-100 font-medium py-3 px-8 rounded-lg text-lg transition-colors duration-200"
              >
                Start Creating Now
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;