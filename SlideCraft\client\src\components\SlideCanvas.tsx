import React, { useRef, useCallback } from 'react';
import { useDrop } from 'react-dnd';
import DraggableComponent from './DraggableComponent.tsx';
import { Slide, SlideComponent } from './SlideBuilder';

interface SlideCanvasProps {
  slide: Slide;
  selectedComponentId: string | null;
  onComponentSelect: (id: string | null) => void;
  onComponentUpdate: (id: string, updates: Partial<SlideComponent>) => void;
  onComponentRemove: (id: string) => void;
}

const SlideCanvas: React.FC<SlideCanvasProps> = ({
  slide,
  selectedComponentId,
  onComponentSelect,
  onComponentUpdate,
  onComponentRemove
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);

  // Handle dropping components on canvas
  const [{ isOver }, drop] = useDrop({
    accept: 'component',
    drop: (item: { id: string }, monitor) => {
      if (!canvasRef.current) return;

      const offset = monitor.getClientOffset();
      const canvasRect = canvasRef.current.getBoundingClientRect();

      if (offset) {
        const x = offset.x - canvasRect.left;
        const y = offset.y - canvasRect.top;

        onComponentUpdate(item.id, { x, y });
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  });

  // Handle canvas click (deselect components)
  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onComponentSelect(null);
    }
  }, [onComponentSelect]);

  // Handle component double-click for text editing
  const handleComponentDoubleClick = useCallback((component: SlideComponent) => {
    if (component.type === 'text') {
      const newContent = prompt('Edit text:', component.content || '');
      if (newContent !== null) {
        onComponentUpdate(component.id, { content: newContent });
      }
    }
  }, [onComponentUpdate]);

  // Handle keyboard events
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (selectedComponentId && e.key === 'Delete') {
      onComponentRemove(selectedComponentId);
    }
  }, [selectedComponentId, onComponentRemove]);

  return (
    <div
      ref={(node) => {
        canvasRef.current = node;
        drop(node);
      }}
      className={`slide-canvas mx-auto max-w-4xl ${isOver ? 'ring-2 ring-blue-400' : ''}`}
      style={{ backgroundColor: slide.backgroundColor }}
      onClick={handleCanvasClick}
      onKeyDown={handleKeyDown}
      tabIndex={0}
    >
      {slide.components.map((component) => (
        <DraggableComponent
          key={component.id}
          component={component}
          isSelected={selectedComponentId === component.id}
          onSelect={() => onComponentSelect(component.id)}
          onUpdate={(updates) => onComponentUpdate(component.id, updates)}
          onDoubleClick={() => handleComponentDoubleClick(component)}
        />
      ))}

      {slide.components.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center text-gray-400 pointer-events-none">
          <div className="text-center">
            <p className="text-lg mb-2">Empty Slide</p>
            <p className="text-sm">Add components from the toolbar above</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default SlideCanvas;