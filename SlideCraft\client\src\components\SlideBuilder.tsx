import React, { useState, useCallback } from 'react';
import { useDrop } from 'react-dnd';
import SlideCanvas from './SlideCanvas.tsx';
import SlideList from './SlideList.tsx';
import ComponentToolbar from './ComponentToolbar.tsx';
import PropertiesPanel from './PropertiesPanel.tsx';

export interface SlideComponent {
  id: string;
  type: 'text' | 'image' | 'shape';
  content?: string;
  x: number;
  y: number;
  width: number;
  height: number;
  style?: React.CSSProperties;
  fontSize?: number;
  fontFamily?: string;
  color?: string;
  backgroundColor?: string;
}

export interface Slide {
  id: string;
  type: 'title' | 'image-text' | 'bullet-list' | 'chart';
  components: SlideComponent[];
  backgroundColor: string;
  layout?: string;
}

interface SlideBuilderProps {
  slides: Slide[];
  onSlidesChange: (slides: Slide[]) => void;
}

const SlideBuilder: React.FC<SlideBuilderProps> = ({ slides, onSlidesChange }) => {
  const [selectedSlideIndex, setSelectedSlideIndex] = useState<number>(0);
  const [selectedComponentId, setSelectedComponentId] = useState<string | null>(null);

  const currentSlide = slides[selectedSlideIndex];
  const selectedComponent = currentSlide?.components.find(c => c.id === selectedComponentId);

  // Add a new slide
  const addSlide = useCallback((type: Slide['type']) => {
    const newSlide: Slide = {
      id: `slide-${Date.now()}`,
      type,
      components: [],
      backgroundColor: '#ffffff',
    };

    const newSlides = [...slides, newSlide];
    onSlidesChange(newSlides);
    setSelectedSlideIndex(newSlides.length - 1);
  }, [slides, onSlidesChange]);

  // Remove a slide
  const removeSlide = useCallback((index: number) => {
    if (slides.length <= 1) return; // Don't remove the last slide

    const newSlides = slides.filter((_, i) => i !== index);
    onSlidesChange(newSlides);

    if (selectedSlideIndex >= newSlides.length) {
      setSelectedSlideIndex(newSlides.length - 1);
    }
    setSelectedComponentId(null);
  }, [slides, selectedSlideIndex, onSlidesChange]);

  // Add component to current slide
  const addComponent = useCallback((componentType: SlideComponent['type']) => {
    if (!currentSlide) return;

    const newComponent: SlideComponent = {
      id: `component-${Date.now()}`,
      type: componentType,
      content: componentType === 'text' ? 'Double click to edit' : '',
      x: 50,
      y: 50,
      width: componentType === 'text' ? 200 : 150,
      height: componentType === 'text' ? 50 : 100,
      fontSize: 16,
      fontFamily: 'Arial',
      color: '#000000',
      backgroundColor: componentType === 'shape' ? '#3b82f6' : 'transparent',
    };

    const updatedSlides = [...slides];
    updatedSlides[selectedSlideIndex] = {
      ...currentSlide,
      components: [...currentSlide.components, newComponent]
    };

    onSlidesChange(updatedSlides);
    setSelectedComponentId(newComponent.id);
  }, [currentSlide, slides, selectedSlideIndex, onSlidesChange]);

  // Update component
  const updateComponent = useCallback((componentId: string, updates: Partial<SlideComponent>) => {
    if (!currentSlide) return;

    const updatedSlides = [...slides];
    const componentIndex = currentSlide.components.findIndex(c => c.id === componentId);

    if (componentIndex !== -1) {
      updatedSlides[selectedSlideIndex] = {
        ...currentSlide,
        components: currentSlide.components.map(c =>
          c.id === componentId ? { ...c, ...updates } : c
        )
      };
      onSlidesChange(updatedSlides);
    }
  }, [currentSlide, slides, selectedSlideIndex, onSlidesChange]);

  // Remove component
  const removeComponent = useCallback((componentId: string) => {
    if (!currentSlide) return;

    const updatedSlides = [...slides];
    updatedSlides[selectedSlideIndex] = {
      ...currentSlide,
      components: currentSlide.components.filter(c => c.id !== componentId)
    };

    onSlidesChange(updatedSlides);
    setSelectedComponentId(null);
  }, [currentSlide, slides, selectedSlideIndex, onSlidesChange]);

  // Update slide background
  const updateSlideBackground = useCallback((backgroundColor: string) => {
    if (!currentSlide) return;

    const updatedSlides = [...slides];
    updatedSlides[selectedSlideIndex] = {
      ...currentSlide,
      backgroundColor
    };
    onSlidesChange(updatedSlides);
  }, [currentSlide, slides, selectedSlideIndex, onSlidesChange]);

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      {/* Left Sidebar - Slide List */}
      <div className="w-64 bg-white dark:bg-gray-800 shadow-lg">
        <SlideList
          slides={slides}
          selectedSlideIndex={selectedSlideIndex}
          onSlideSelect={setSelectedSlideIndex}
          onSlideAdd={addSlide}
          onSlideRemove={removeSlide}
        />
      </div>

      {/* Main Canvas Area */}
      <div className="flex-1 flex flex-col">
        {/* Component Toolbar */}
        <ComponentToolbar onAddComponent={addComponent} />

        {/* Slide Canvas */}
        <div className="flex-1 p-6 overflow-auto">
          {currentSlide ? (
            <SlideCanvas
              slide={currentSlide}
              selectedComponentId={selectedComponentId}
              onComponentSelect={setSelectedComponentId}
              onComponentUpdate={updateComponent}
              onComponentRemove={removeComponent}
            />
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              No slide selected
            </div>
          )}
        </div>
      </div>

      {/* Right Sidebar - Properties Panel */}
      <div className="w-80 bg-white dark:bg-gray-800 shadow-lg">
        <PropertiesPanel
          slide={currentSlide}
          selectedComponent={selectedComponent}
          onComponentUpdate={updateComponent}
          onSlideUpdate={updateSlideBackground}
        />
      </div>
    </div>
  );
};

export default SlideBuilder;